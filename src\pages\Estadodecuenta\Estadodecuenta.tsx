import React, { useState, useRef, useEffect } from 'react';
import { FA6 } from '@/config';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS } from '@/config/authConfig';
import { useColaboradorData } from './estadodecuenta.ts';
import { useDatosPersonales } from '@/hooks';
import { useAnimation } from '@/context/AnimationContext';
import { useUserStatusCheck } from '@/hooks';
import { CuentaInactiva } from '@/pages/Auth';

interface EyeToggleIconProps {
  isExpanded: boolean;
  className?: string;
  size?: number;
}

const EyeToggleIcon: React.FC<EyeToggleIconProps> = ({
  isExpanded,
  size = 18
}) => {
  return (
    <button
      type="button"
      className="btn btn-primary rounded-3 shadow-sm d-inline-flex align-items-center justify-content-center position-relative"
      style={{
        width: '40px',
        height: '40px',
        border: 'none',
        transition: 'all 0.25s ease-in-out',
        transform: isExpanded ? 'scale(1.05)' : 'scale(1)',
        boxShadow: '0 4px 15px rgba(var(--bs-primary-rgb), 0.3), 0 2px 8px rgba(var(--bs-primary-rgb), 0.2)'
      }}
      title={isExpanded ? 'Ocultar detalles' : 'Ver detalles'}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = isExpanded ? 'scale(1.1)' : 'scale(1.1)';
        e.currentTarget.style.boxShadow = '0 6px 20px rgba(var(--bs-primary-rgb), 0.4), 0 3px 12px rgba(var(--bs-primary-rgb), 0.3)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = isExpanded ? 'scale(1.05)' : 'scale(1)';
        e.currentTarget.style.boxShadow = '0 4px 15px rgba(var(--bs-primary-rgb), 0.3), 0 2px 8px rgba(var(--bs-primary-rgb), 0.2)';
      }}
      onMouseDown={(e) => {
        e.currentTarget.style.transform = 'scale(0.95)';
      }}
      onMouseUp={(e) => {
        setTimeout(() => {
          e.currentTarget.style.transform = isExpanded ? 'scale(1.05)' : 'scale(1)';
        }, 100);
      }}
    >
      <div
        className="d-flex align-items-center justify-content-center"
        style={{
          transition: 'transform 0.3s ease-in-out',
          transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'
        }}
      >
        {isExpanded ? (
          <FA6.FaEyeSlash
            className="text-white"
            size={size}
            style={{ filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' }}
          />
        ) : (
          <FA6.FaEye
            className="text-white"
            size={size}
          />
        )}
      </div>
    </button>
  );
};

interface TabConfig {
  id: string;
  label: string;
  data: any[];
  rawData: any[];
  expandedCompanies: Set<number>;
  setExpandedCompanies: React.Dispatch<React.SetStateAction<Set<number>>>;
  animatingCompanies: Set<number>;
  setAnimatingCompanies: React.Dispatch<React.SetStateAction<Set<number>>>;
  detailRefs: React.MutableRefObject<Map<number, HTMLElement>>;
  summaryCards: SummaryCardConfig[];
  progressConfig: ProgressConfig;
  tableConfig: TableConfig;
}

interface SummaryCardConfig {
  icon: React.ComponentType<any>;
  iconBg: string;
  iconColor: string;
  title: string;
  value: string;
}

interface ProgressConfig {
  title: string | React.ReactNode;
  percentage: number;
  label: string;
}

interface TableConfig {
  title: string;
  headers: string[];
  columns: TableColumnConfig[];
  detailTitle: string;
  detailHeaders: string[];
  emptyMessage: string;
}

interface TableColumnConfig {
  key: string;
  render: (group: any) => React.ReactNode;
  className?: string;
}

const SummaryCards: React.FC<{ cards: SummaryCardConfig[] }> = ({ cards }) => (
  <div className="d-flex flex-wrap justify-content-center gap-4 mb-1">
    {cards.map((card, index) => (
      <div key={index} className="flex-fill" style={{minWidth: '100px', maxWidth: '220px'}}>
        <div className="card shadow-sm rounded">
          <div className="card-body d-flex flex-column align-items-center justify-content-center text-center py-1 px-1" style={{minHeight: '140px'}}>
            <div className={`${card.iconBg} mb-2 d-flex align-items-center justify-content-center rounded-circle`} style={{width: '50px', height: '50px'}}>
              <card.icon className={card.iconColor} size={22} />
            </div>
            <h6 className="fw-bold mb-2">{card.title}</h6>
            <h3 className={`mb-0 fw-bold ${card.iconColor}`}>{card.value}</h3>
          </div>
        </div>
      </div>
    ))}
  </div>
);

const ProgressBar: React.FC<{ config: ProgressConfig }> = ({ config }) => (
  <div className="d-flex justify-content-center mb-1">
    <div className="w-100" style={{maxWidth: '500px'}}>
      <div className="card border-0 shadow-sm rounded mb-2">
        <div className="card-body py-2 px-3">
          <div className="d-flex justify-content-between align-items-center mb-2">
            <h6 className="fw-bold mb-0">
              <FA6.FaChartPie className="me-2 text-primary" />
              {config.title}
            </h6>
            <span className="badge bg-primary rounded-pill">
              {config.label}
            </span>
          </div>
          <div className="progress" style={{ height: '8px' }}>
            <div
              className="progress-bar bg-primary"
              role="progressbar"
              style={{
                width: `${Math.min(config.percentage, 100).toFixed(2)}%`
              }}
              aria-valuenow={Math.min(parseFloat(config.percentage.toFixed(2)), 100)}
              aria-valuemin={0}
              aria-valuemax={100}
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

interface MovementTableProps {
  config: TabConfig;
  iconStyles: any;
  handleToggleExpand: (companyCode: number, isCredit: boolean) => void;
  isCredit: boolean;
}

const MovementTable: React.FC<MovementTableProps> = ({ config, handleToggleExpand, isCredit }) => (
  <div className="card border-0 shadow rounded-4 overflow-hidden">
    <div className="card-header bg-gradient bg-dark py-3 px-4 border-0">
      <h5 className="fw-bold mb-0 text-white d-flex align-items-center">
        <div className="bg-primary bg-opacity-25 rounded-circle p-2 me-3">
          <FA6.FaMoneyBillTransfer className="text-white" size={16} />
        </div>
        {config.tableConfig.title}
      </h5>
    </div>
    <div className="card-body p-0">
      <div className="table-responsive">
        <table className="table align-middle mb-0">
          <thead className="table-dark sticky-top">
            <tr>
              {config.tableConfig.headers.map((header, index) => (
                <th key={index} className="py-3 px-4 text-center fw-semibold text-light border-0 position-relative">
                  {header}
                  {index < config.tableConfig.headers.length - 1 && (
                    <div className="position-absolute top-50 end-0 translate-middle-y bg-secondary bg-opacity-25"
                         style={{width: '1px', height: '60%'}}></div>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {config.data?.length === 0 ? (
              <tr>
                <td colSpan={config.tableConfig.headers.length} className="text-center py-5">
                  <div className="text-muted">
                    <div className="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style={{width: '60px', height: '60px'}}>
                      <FA6.FaCircleInfo className="text-secondary" size={24} />
                    </div>
                    <p className="mb-0 fw-medium">{config.tableConfig.emptyMessage}</p>
                  </div>
                </td>
              </tr>
            ) : (
              config.data?.map((group) => (
                <React.Fragment key={group.companyCode}>
                  <tr className="border-0">
                    <td className="py-4 px-4 border-0">
                      <div className="d-flex align-items-center">
                        <div className="bg-primary bg-opacity-10 border border-primary border-opacity-25 rounded-3 me-3 d-flex align-items-center justify-content-center"
                             style={{width: '48px', height: '48px'}}>
                          <FA6.FaBuilding className="text-primary" size={20} />
                        </div>
                        <div>
                          <h6 className="fw-bold mb-1 text-dark">{group.companyName}</h6>
                          <small className="text-muted fw-medium">Código: {group.companyCode}</small>
                        </div>
                      </div>
                    </td>
                    {config.tableConfig.columns.map((column, index) => (
                      <td key={index} className="py-4 px-4 text-center border-0">
                        <div className="d-flex justify-content-center">
                          {column.render(group)}
                        </div>
                      </td>
                    ))}
                    <td className="py-4 px-4 text-center border-0">
                      <div
                        onClick={() => handleToggleExpand(group.companyCode, isCredit)}
                        className="d-inline-block"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleToggleExpand(group.companyCode, isCredit);
                          }
                        }}
                      >
                        <EyeToggleIcon isExpanded={config.expandedCompanies.has(group.companyCode)} />
                      </div>
                    </td>
                  </tr>
                  {config.expandedCompanies.has(group.companyCode) && (
                    <tr>
                      <td colSpan={config.tableConfig.headers.length} className="p-0 border-0">
                        <div
                          ref={(el) => {
                            if (el) config.detailRefs.current.set(group.companyCode, el);
                          }}
                          className={`detail-content ${
                            config.animatingCompanies.has(group.companyCode) ? 'detail-expand-enter' : 'detail-expand-enter-active'
                          }`}
                          style={{
                            background: 'linear-gradient(to bottom, rgba(248, 249, 250, 0.8) 0%, rgba(233, 236, 239, 0.6) 100%)',
                            borderTop: '2px solid rgba(108, 117, 125, 0.15)',
                            borderLeft: '3px solid rgba(108, 117, 125, 0.1)'
                          }}
                        >
                          <div className="table-responsive">
                            <table className="table align-middle mb-0">
                              <thead className="bg-dark bg-opacity-85 sticky-top">
                                <tr>
                                  {config.tableConfig.detailHeaders.map((header, index) => (
                                    <th key={index} className="py-3 px-4 text-white fw-semibold border-0 position-relative">
                                      {header}
                                      {index < config.tableConfig.detailHeaders.length - 1 && (
                                        <div className="position-absolute top-50 end-0 translate-middle-y bg-white bg-opacity-25"
                                             style={{width: '1px', height: '60%'}}></div>
                                      )}
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody>
                                {config.rawData
                                  ?.filter(movement => movement.CODEMP === group.companyCode)
                                  .map((movement, index) => (
                                    <tr key={movement.NROTRANS} className={`border-0 ${index % 2 === 0 ? 'bg-white bg-opacity-80' : 'bg-light bg-opacity-40'}`}>
                                      <td className="py-3 px-4 border-0">
                                        <span className="fw-medium text-dark">
                                          {new Date(movement.FECHA).toLocaleDateString('es-ES', {
                                            day: '2-digit',
                                            month: '2-digit',
                                            year: 'numeric'
                                          })}
                                        </span>
                                      </td>
                                      <td className="py-3 px-4 border-0">
                                        <span className={`badge rounded-pill px-3 py-2 fw-medium ${
                                          movement.TIPO_COMPROBANTE === 'FB' ? 'bg-primary bg-opacity-90 text-white' :
                                          movement.TIPO_COMPROBANTE === 'RC' ? 'bg-success bg-opacity-90 text-white' :
                                          movement.TIPO_COMPROBANTE === 'CB' ? 'bg-success bg-opacity-90 text-white' :
                                          'bg-info bg-opacity-90 text-white'
                                        }`}>
                                          {movement.TIPO_COMPROBANTE === 'FB' ? (isCredit ? 'Factura' : 'Compra') :
                                           movement.TIPO_COMPROBANTE === 'CB' ? (isCredit ? 'Comprobante' : 'Devolución') :
                                           movement.TIPO_COMPROBANTE === 'RC' ? (isCredit ? 'Recibo' : 'Pago') : movement.TIPO_COMPROBANTE}
                                        </span>
                                      </td>
                                      <td className="py-3 px-4 border-0">
                                        <span className="fw-medium text-dark font-monospace">
                                          {movement.PREFIJO}-{movement.NUMERO}
                                        </span>
                                      </td>
                                      <td className="py-3 px-4 border-0">
                                        <span className="text-dark-emphasis fw-medium" style={{opacity: 0.8}}>
                                          {movement.NOMBRE_SUCURSAL}
                                        </span>
                                      </td>
                                      <td className="py-3 px-4 text-end border-0">
                                        <span className={`fw-bold fs-6 ${
                                          movement.TIPO_COMPROBANTE === 'FB' ? 'text-danger' : 'text-success'
                                        }`}>
                                          $ {parseFloat(movement.TOTAL_EN_DOLARES).toFixed(2)}
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  </div>
);

const EstadoDeCuenta: React.FC & { pageMetadata: PageMetadata } = () => {
  const datosPersonales = useDatosPersonales();
  const { animateIn, durations, classes, smoothScrollTo, timingFunctions } = useAnimation();
  const { status: userStatus, loading: statusLoading, checkStatus } = useUserStatusCheck();

  useEffect(() => {
    const refreshData = async () => {
      try {
        await datosPersonales.refetchDatosPersonales();
      } catch (error) {
        console.error('[EstadoDeCuenta] Error refreshing personal data:', error);
      }
    };

    refreshData();
  }, []);

  const {
    rawMovements,
    processedMovements,
    rawCashMovements,
    processedCashMovements,
    currentMonthCashTotals,
    loading,
    error
  } = useColaboradorData();

  const [expandedCompanies, setExpandedCompanies] = useState<Set<number>>(new Set());
  const [expandedCashCompanies, setExpandedCashCompanies] = useState<Set<number>>(new Set());
  const [activeTab, setActiveTab] = useState('credito');
  const [animatingCompanies, setAnimatingCompanies] = useState<Set<number>>(new Set());
  const [animatingCashCompanies, setAnimatingCashCompanies] = useState<Set<number>>(new Set());
  const [isVerifying, setIsVerifying] = useState(false);

  const detailRefs = useRef<Map<number, HTMLElement>>(new Map());
  const cashDetailRefs = useRef<Map<number, HTMLElement>>(new Map());
  const totalUsedCredit = processedMovements?.reduce((total, group) => total + group.balance, 0) || 0;

  const scrollToElement = (element: HTMLElement) => {
    const performScroll = () => {
      const isMobile = window.innerWidth < 768;

      if (isMobile) {
        smoothScrollTo(element, {
          offset: 10,
          duration: durations.scroll,
          easing: timingFunctions.easeOutCubic
        });
        return;
      }

      let table = element.closest('table');
      if (!table) {
        const container = element.closest('.table-responsive');
        table = container?.querySelector('table') || null;
      }
      if (!table) {
        const activeTab = document.querySelector('.tab-pane.active');
        const tableContainer = activeTab?.querySelector('.table-responsive table');
        table = tableContainer as HTMLTableElement;
      }

      const tableHeader = table?.querySelector('thead');
      if (tableHeader) {
        const headerRect = tableHeader.getBoundingClientRect();
        const distance = Math.abs(headerRect.top);
        let scrollDuration = durations.scroll;
        let scrollEasing = timingFunctions.easeOutCubic;

        if (distance > 800) {
          scrollDuration = durations.scrollSlow;
          scrollEasing = timingFunctions.easeInOutQuad;
        } else if (distance > 400) {
          scrollDuration = durations.scroll;
          scrollEasing = timingFunctions.easeOutCubic;
        } else {
          scrollDuration = Math.max(durations.scroll * 0.7, 600);
          scrollEasing = timingFunctions.easeOutCubic;
        }

        smoothScrollTo(tableHeader, {
          offset: 20,
          duration: scrollDuration,
          easing: scrollEasing
        });
      } else {
        smoothScrollTo(element, {
          offset: 20,
          duration: durations.scroll,
          easing: timingFunctions.easeOutCubic
        });
      }
    };

    requestAnimationFrame(() => {
      setTimeout(performScroll, 150);
    });
  };
  const rawCreditLimit = datosPersonales.datosPersonales?.LimiteCredito || '0';
  const creditLimit = parseFloat(String(rawCreditLimit)) || 0;

  const availableCredit = Math.max(0, creditLimit - totalUsedCredit);
  const usagePercentage = creditLimit > 0 ? (totalUsedCredit / creditLimit) * 100 : 0;
  const currentMonthName = new Date().toLocaleString('es-ES', { month: 'long' });
  const totalUsedCash = currentMonthCashTotals?.balance || 0;
  const availableCashCredit = Math.max(0, creditLimit - totalUsedCash);
  const cashUsagePercentage = creditLimit > 0 ? (totalUsedCash / creditLimit) * 100 : 0;

  const iconStyles = {
    iconCircle: {
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },
    iconSquare: {
      width: '40px',
      height: '40px',
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  };

  const tabConfigs: Record<string, TabConfig> = {
    credito: {
      id: 'credito',
      label: 'Crédito',
      data: processedMovements || [],
      rawData: rawMovements || [],
      expandedCompanies,
      setExpandedCompanies,
      animatingCompanies,
      setAnimatingCompanies,
      detailRefs,
      summaryCards: [
        {
          icon: FA6.FaCreditCard,
          iconBg: 'bg-primary-subtle',
          iconColor: 'text-primary',
          title: 'Limite de crédito	',
          value: `$ ${datosPersonales.limiteCredito}`
        },
        {
          icon: FA6.FaMoneyBillTransfer,
          iconBg: 'bg-warning-subtle',
          iconColor: 'text-danger',
          title: 'Utilizado',
          value: `$ ${totalUsedCredit.toFixed(2)}`
        },
        {
          icon: FA6.FaMoneyBillTrendUp,
          iconBg: 'bg-success-subtle',
          iconColor: 'text-success',
          title: 'Disponible',
          value: `$ ${availableCredit.toFixed(2)}`
        }
      ],
      progressConfig: {
        title: 'Uso de Crédito',
        percentage: usagePercentage,
        label: usagePercentage > 100 ? "100%+ Utilizado" : `${usagePercentage.toFixed(2)}% Utilizado`
      },
      tableConfig: {
        title: 'Historico de Compras',
        headers: ['Empresa', 'Facturas', 'Recibos', 'Utilizado', 'Detalles'],
        columns: [
          {
            key: 'fbTotal',
            render: (group) => <span className="fw-bold text-primary fs-6">$ {group.fbTotal.toFixed(2)}</span>,
            className: 'fw-bold text-primary'
          },
          {
            key: 'cbRcTotal',
            render: (group) => <span className="fw-bold text-success fs-6">$ {group.cbRcTotal.toFixed(2)}</span>,
            className: 'fw-bold text-success'
          },
          {
            key: 'balance',
            render: (group) => <span className="fw-bold text-danger fs-6">$ {group.balance.toFixed(2)}</span>,
            className: 'fw-bold text-danger'
          }
        ],
        detailTitle: 'Detalle de Movimientos',
        detailHeaders: ['Fecha', 'Tipo', 'Comprobante', 'Sucursal', 'Monto'],
        emptyMessage: 'No hay movimientos disponibles'
      }
    },
    contado: {
      id: 'contado',
      label: 'Contado',
      data: processedCashMovements || [],
      rawData: rawCashMovements || [],
      expandedCompanies: expandedCashCompanies,
      setExpandedCompanies: setExpandedCashCompanies,
      animatingCompanies: animatingCashCompanies,
      setAnimatingCompanies: setAnimatingCashCompanies,
      detailRefs: cashDetailRefs,
      summaryCards: [
        {
          icon: FA6.FaWallet,
          iconBg: 'bg-primary-subtle',
          iconColor: 'text-primary',
          title: 'Limite en el mes',
          value: `$ ${datosPersonales.limiteCredito}`
        },
        {
          icon: FA6.FaMoneyCheck,
          iconBg: 'bg-warning-subtle',
          iconColor: 'text-danger',
          title: 'Utilizado en el mes',
          value: `$ ${totalUsedCash.toFixed(2)}`
        },
        {
          icon: FA6.FaMoneyBillTrendUp,
          iconBg: 'bg-success-subtle',
          iconColor: 'text-success',
          title: 'Disponible en el mes',
          value: `$ ${availableCashCredit.toFixed(2)}`
        }
      ],
      progressConfig: {
        title: (
          <>
            Utilizado en el mes de{' '}
            <span className="ms-2 badge bg-warning text-dark fw-bold px-3 py-1 rounded-pill shadow-sm">
              {currentMonthName}
            </span>
          </>
        ),
        percentage: cashUsagePercentage,
        label: cashUsagePercentage > 100 ? "100%+ Utilizado" : `${cashUsagePercentage.toFixed(2)}% Utilizado`
      },
      tableConfig: {
        title: 'Historico de Compras',
        headers: ['Empresa', 'Compras', 'Detalles'],
        columns: [
          {
            key: 'balance',
            render: (group) => <span className="fw-bold text-danger fs-6">$ {group.balance.toFixed(2)}</span>,
            className: 'fw-bold text-danger'
          }
        ],
        detailTitle: 'Detalle de Movimientos de Contado',
        detailHeaders: ['Fecha', 'Tipo', 'Comprobante', 'Sucursal', 'Monto'],
        emptyMessage: 'No hay movimientos disponibles'
      }
    }
  };

  const renderTabContent = (tabId: string) => {
    const config = tabConfigs[tabId];
    if (!config) return null;

    return (
      <div className={`tab-pane p-3 ${activeTab === tabId ? 'active' : ''}`} id={tabId} role="tabpanel">
        <SummaryCards cards={config.summaryCards} />
        <ProgressBar config={config.progressConfig} />
        <MovementTable
          config={config}
          iconStyles={iconStyles}
          handleToggleExpand={handleToggleExpand}
          isCredit={tabId === 'credito'}
        />
      </div>
    );
  };

  const handleToggleExpand = async (companyCode: number, isCredit: boolean = true) => {
    const expandedSet = isCredit ? expandedCompanies : expandedCashCompanies;
    const setExpandedSet = isCredit ? setExpandedCompanies : setExpandedCashCompanies;
    const setAnimatingSet = isCredit ? setAnimatingCompanies : setAnimatingCashCompanies;
    const refs = isCredit ? detailRefs : cashDetailRefs;

    const isExpanded = expandedSet.has(companyCode);
    const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (!isExpanded) {
      setExpandedSet(prev => new Set(prev).add(companyCode));
      setAnimatingSet(prev => new Set(prev).add(companyCode));

      requestAnimationFrame(() => {
        const element = refs.current.get(companyCode);
        if (element) {
          if (reducedMotion) {
            element.style.opacity = '1';
            element.style.transform = 'none';
            scrollToElement(element);
            setAnimatingSet(prev => {
              const newSet = new Set(prev);
              newSet.delete(companyCode);
              return newSet;
            });
          } else {
            animateIn(element, classes.slideInDown, durations.fast).then(() => {
              scrollToElement(element);
              setAnimatingSet(prev => {
                const newSet = new Set(prev);
                newSet.delete(companyCode);
                return newSet;
              });
            });
          }
        }
      });
    } else {
      setAnimatingSet(prev => new Set(prev).add(companyCode));
      const element = refs.current.get(companyCode);

      if (element) {
        if (reducedMotion) {
          setExpandedSet(prev => {
            const newSet = new Set(prev);
            newSet.delete(companyCode);
            return newSet;
          });
          setAnimatingSet(prev => {
            const newSet = new Set(prev);
            newSet.delete(companyCode);
            return newSet;
          });
        } else {
          element.style.transition = 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)';
          element.style.opacity = '0';
          element.style.transform = 'translateY(-8px) scale(0.98)';
          element.style.maxHeight = '0px';
          element.style.overflow = 'hidden';
          element.style.paddingTop = '0';
          element.style.paddingBottom = '0';
          element.style.marginTop = '0';
          element.style.marginBottom = '0';

          setTimeout(() => {
            setExpandedSet(prev => {
              const newSet = new Set(prev);
              newSet.delete(companyCode);
              return newSet;
            });
            setAnimatingSet(prev => {
              const newSet = new Set(prev);
              newSet.delete(companyCode);
              return newSet;
            });
          }, 250);
        }
      }
    }
  };

  const handleVerifyStatus = async () => {
    if (statusLoading || isVerifying) {
      return;
    }

    setIsVerifying(true);
    try {
      await Promise.all([
        checkStatus(),
        datosPersonales.refetchDatosPersonales()
      ]);
    } catch (error) {
      console.error('[EstadoDeCuenta] Error verifying status:', error);
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    return () => {
      detailRefs.current.clear();
      cashDetailRefs.current.clear();
    };
  }, []);



  if (loading) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Cargando datos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center text-danger">
          <FA6.FaTriangleExclamation size={32} />
          <p className="mt-2">Error: {error.message}</p>
          <button className="btn btn-primary mt-2" onClick={() => window.location.reload()}>Reintentar</button>
        </div>
      </div>
    );
  }

  if (statusLoading) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Verificando estado...</span>
          </div>
          <p className="mt-2">Verificando estado del usuario...</p>
        </div>
      </div>
    );
  }

  if (userStatus === 'inactive') {
    return (
      <CuentaInactiva
        handleVerifyStatus={handleVerifyStatus}
        isVerifying={isVerifying}
        statusLoading={statusLoading}
      />
    );
  }

  return (
    <>
      <style>{`
        .estado-cuenta-tabs .nav-tabs {
          border-bottom: none;
          margin-bottom: 1.5rem;
          gap: 0.5rem;
        }
        .estado-cuenta-tabs .nav-tabs .nav-link {
          border: 2px solid rgba(var(--bs-primary-rgb), 0.15);
          background: rgba(255, 255, 255, 0.95);
          color: #6b7280;
          border-radius: 12px;
          padding: 0.65rem 1.5rem;
          margin-right: 0;
          font-weight: 600;
          font-size: 0.875rem;
          position: relative;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow:
            0 4px 8px -2px rgba(0, 0, 0, 0.08),
            0 2px 4px -1px rgba(0, 0, 0, 0.04);
          backdrop-filter: blur(8px);
          will-change: transform, box-shadow;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          overflow: hidden;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.05) 0%, transparent 50%);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 10px;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link:hover {
          background: rgba(var(--bs-primary-rgb), 0.05);
          color: var(--bs-primary);
          border-color: rgba(var(--bs-primary-rgb), 0.3);
          box-shadow:
            0 8px 16px -4px rgba(var(--bs-primary-rgb), 0.15),
            0 4px 8px -2px rgba(var(--bs-primary-rgb), 0.1);
          transform: translateY(-2px);
        }

        .estado-cuenta-tabs .nav-tabs .nav-link:hover::after {
          opacity: 1;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link.active {
          background: var(--bs-primary) !important;
          color: #ffffff !important;
          font-weight: 700;
          border: 2px solid var(--bs-primary);
          box-shadow:
            0 8px 20px -4px rgba(var(--bs-primary-rgb), 0.4),
            0 4px 12px -2px rgba(var(--bs-primary-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
          transform: translateY(-3px);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .estado-cuenta-tabs .nav-tabs .nav-link.active::after {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
          opacity: 1;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link:active {
          transform: translateY(-1px) scale(0.98);
          transition-duration: 0.1s;
          box-shadow:
            0 2px 8px -1px rgba(0, 0, 0, 0.15),
            0 1px 4px -1px rgba(0, 0, 0, 0.1);
        }
        .estado-cuenta-tabs .card,
        .estado-cuenta-tabs .border-0.shadow-sm.rounded {
          box-shadow: var(--bs-card-box-shadow) !important;
          border: 1px solid var(--bs-border-color) !important;
          border-radius: 16px !important;
          backdrop-filter: blur(10px);
          background: var(--bs-card-bg) !important;
        }

        @media (prefers-reduced-motion: reduce) {
          .estado-cuenta-tabs .nav-tabs .nav-link,
          .detail-content,
          .detail-expand-enter,
          .detail-expand-exit {
            transition: none !important;
            animation: none !important;
          }
        }

        .detail-expand-enter {
          opacity: 0;
          transform: translateY(-12px) scale(0.96);
          max-height: 0;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
          will-change: transform, opacity, max-height;
        }

        .detail-expand-enter-active {
          opacity: 1;
          transform: translateY(0) scale(1);
          max-height: 2000px;
        }

        .detail-expand-exit {
          opacity: 1;
          transform: translateY(0) scale(1);
          max-height: 2000px;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          will-change: transform, opacity, max-height;
        }

        .detail-expand-exit-active {
          opacity: 0;
          transform: translateY(-8px) scale(0.98);
          max-height: 0;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
          margin-top: 0 !important;
          margin-bottom: 0 !important;
        }

        .detail-content {
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          border-radius: 12px;
          contain: layout style paint;
        }

        .detail-content.animating {
          pointer-events: none;
          will-change: transform, opacity;
        }

        @keyframes smoothSlideIn {
          from {
            opacity: 0;
            transform: translateY(-16px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes smoothSlideOut {
          from {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
          to {
            opacity: 0;
            transform: translateY(-12px) scale(0.96);
          }
        }

        .smooth-slide-in {
          animation: smoothSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        .smooth-slide-out {
          animation: smoothSlideOut 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        html {
          scroll-behavior: smooth;
        }

        @media (prefers-reduced-motion: reduce) {
          html {
            scroll-behavior: auto;
          }
        }

        /* Enhanced scroll animation styles */
        .smooth-scroll-target {
          scroll-margin-top: 20px;
        }

        .scroll-indicator {
          transition: opacity 0.3s ease-out;
        }

        /* Ensure smooth scrolling performance */
        * {
          scroll-behavior: smooth;
        }

        @supports (scroll-behavior: smooth) {
          html {
            scroll-behavior: smooth;
          }
        }

        /* Professional Table Styling - Hover effects removed to prevent scroll bar issues */

        /* Enhanced card styling */
        .card.rounded-4 {
          border-radius: 1rem !important;
          overflow: hidden;
          backdrop-filter: blur(10px);
        }

        .card-header.bg-gradient {
          background: linear-gradient(135deg, var(--bs-dark) 0%, #2c3e50 100%) !important;
        }

        /* Smooth transitions for all interactive elements */
        .transition-all {
          transition: all 0.25s ease-in-out;
        }

        /* Badge improvements */
        .badge.rounded-pill {
          font-size: 0.75rem;
          font-weight: 600;
          letter-spacing: 0.025em;
          text-transform: uppercase;
        }

        /* Money amount styling */
        .fw-bold.fs-6 {
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
          letter-spacing: 0.025em;
        }

        /* Disable animations for reduced motion preference */
        @media (prefers-reduced-motion: reduce) {
          .transition-all,
          .card,
          .btn {
            transition: none !important;
            animation: none !important;
          }
        }

      `}</style>

        {/* Estado de Cuenta Tabbed Card */}
        <div className="card border-0 shadow-sm rounded">
          <div className="card-body estado-cuenta-tabs">
            <div className="mb-3 fw-bold fs-4">
              <FA6.FaMoneyBillWave className="me-2" />
              <span>Estado de Cuenta</span>
            </div>
            <ul className="nav nav-tabs" role="tablist">
              {Object.values(tabConfigs).map((config) => (
                <li key={config.id} className="nav-item">
                  <a
                    className={`nav-link ${activeTab === config.id ? 'active' : ''}`}
                    data-bs-toggle="tab"
                    href={`#${config.id}`}
                    role="tab"
                    onClick={(e) => {
                      e.preventDefault();
                      setActiveTab(config.id);
                    }}
                  >
                    <span>{config.label}</span>
                  </a>
                </li>
              ))}
            </ul>
            <div className="tab-content">
              {Object.keys(tabConfigs).map((tabId) => renderTabContent(tabId))}
            </div>
          </div>
        </div>

    </>
  );
};

// Define page metadata
EstadoDeCuenta.pageMetadata = {
  title: 'Movimientos',
  description: 'Movimientos de Cuenta',
  icon: <FA6.FaMoneyBillTransfer className="fs-6" />,
  showInMenu: true,
  menuOrder: 2,
  path: '/main/estadodecuenta',
  requiresAuth: true,
  section: 'PERSONA',
  parent: 'Persona',
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'estadodecuenta'
  }
};

export default EstadoDeCuenta;
