# Menu Architecture Documentation

## Overview

This document describes the scalable menu architecture that allows creating menu groups/submenus without requiring dummy page files.

## Architecture Components

### 1. Menu Groups Configuration (`src/config/menuGroups.ts`)

This file centralizes all menu group definitions. Menu groups are containers that group related pages together in the navigation menu.

```typescript
const menuGroupsConfig: Record<string, MenuGroup> = {
  'Persona': {
    key: 'Persona',
    title: 'Persona',
    icon: getIcon('FaUser', { className: 'fs-6' }),
    section: 'PERSONA',
    menuOrder: 2,
    showInMenu: true
  }
};
```

### 2. Page Metadata Configuration

Pages specify which menu group they belong to using the `parent` property in their pageMetadata:

```typescript
// In src/pages/Miperfil/Miperfil.tsx
Miperfil.pageMetadata = {
  title: 'Datos Personales',
  description: 'Datos Personales',
  icon: <FA6.FaUserTie className="fs-6" />,
  showInMenu: true,
  menuOrder: 1,
  path: '/main/miperfil',
  requiresAuth: true,
  section: 'PERSO<PERSON>',
  parent: 'Persona', // This groups it under the Persona menu group
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'miperfil'
  }
};
```

### 3. Navigation Building Logic

The `buildNavigationFromRoutes` function in `src/routes/PageMetadata.ts` automatically:

1. Creates navigation items from regular pages
2. Creates navigation items from menu groups that have child pages
3. Groups child pages under their parent menu groups
4. Sorts items by menuOrder

## How to Add New Menu Groups

### Step 1: Define the Menu Group

Add your menu group to `src/config/menuGroups.ts`:

```typescript
const menuGroupsConfig: Record<string, MenuGroup> = {
  'Persona': {
    key: 'Persona',
    title: 'Persona',
    icon: getIcon('FaUser', { className: 'fs-6' }),
    section: 'PERSONA',
    menuOrder: 2,
    showInMenu: true
  },
  'Reports': {
    key: 'Reports',
    title: 'Reportes',
    icon: getIcon('FaChartBar', { className: 'fs-6' }),
    section: 'REPORTS',
    menuOrder: 3,
    showInMenu: true
  }
};
```

### Step 2: Update Page Metadata

Set the `parent` property in your pages' pageMetadata to match the group key:

```typescript
// In your page component
MyReportPage.pageMetadata = {
  title: 'Sales Report',
  description: 'Monthly sales report',
  icon: <FA6.FaChartLine className="fs-6" />,
  showInMenu: true,
  menuOrder: 1,
  path: '/main/reports/sales',
  requiresAuth: true,
  section: 'REPORTS',
  parent: 'Reports', // Groups it under the Reports menu group
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'reports'
  }
};
```

## Benefits of This Architecture

1. **No Dummy Files**: No need to create dummy page files for menu grouping
2. **Centralized Configuration**: All menu groups are defined in one place
3. **Scalable**: Easy to add new menu groups and pages
4. **Dynamic**: Menu groups only appear if they have child pages
5. **Automatic Icon Inheritance**: Groups can inherit icons from their first child if not specified
6. **Proper Routing**: Only actual pages have routes, groups are just UI containers

## Current Menu Structure

```
├── HOME
│   └── Inicio (Dashboard)
├── PERSONA
│   └── Persona (Menu Group)
│       ├── Datos Personales (Miperfil)
│       └── Movimientos (Estadodecuenta)
└── CONFIGURACIÓN
    └── Settings
```

## Migration Notes

- Removed `src/pages/Persona/index.ts` (was causing dynamic module loading errors)
- Pages with `parent: 'Persona'` are automatically grouped under the Persona menu group
- The menu system handles parent-child relationships automatically
- No changes needed to existing page components, only their metadata configuration
