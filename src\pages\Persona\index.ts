import { FA6 } from '@/config/icons/iconUtils';
import { registerPageModule } from '@/routes/PageMetadata';
import { PERMISSIONS } from '@/config/authConfig';

// Create a virtual parent component for Persona section
const PersonaParent = () => null;

PersonaParent.pageMetadata = {
  title: 'Persona',
  description: 'Información Personal',
  icon: <FA6.FaUser className="fs-6" />,
  showInMenu: true,
  menuOrder: 2,
  requiresAuth: true,
  section: 'PERSONA',
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'persona'
  }
};

// Register the parent module without a path (it's just a container)
try {
  registerPageModule('Persona', PersonaParent, PersonaParent.pageMetadata);
} catch (error) {
  console.error('[PERSONA] Error registering Persona parent module:', error);
}

export const pageMetadata = PersonaParent.pageMetadata;
export default PersonaParent;
