import React, { useState, useEffect } from 'react';
import { Link, Location } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';
import { useLayout } from '@/context/LayoutContext';
import { MAIN_ROUTE_PREFIX } from '@/routes/routeConstants';
import { getPageSectionInfo } from '@/hoc/withPageMetadata';
import classNames from 'classnames';

export interface MenuItem {
  key: string;
  path: string;
  title: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  section?: string;
  isGroup?: boolean;
}

interface MenuProps {
  menuItems: MenuItem[];
  location: Location;
}

const ensurePrefixedPath = (path: string | undefined): string | undefined => {
  if (!path) return path;
  if (path.startsWith('/auth') || path.startsWith(MAIN_ROUTE_PREFIX)) {
    return path;
  }
  return `${MAIN_ROUTE_PREFIX}${path.startsWith('/') ? path : '/' + path}`;
};

const Menu: React.FC<MenuProps> = ({ menuItems, location }) => {
  const { settings: layoutSettings } = useLayout();
  const [openKeys, setOpenKeys] = useState<Record<string, boolean>>({});
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  useEffect(() => {
    const currentPath = location.pathname;
    const checkPath = (items: MenuItem[]) => {
      items.forEach(item => {
        const itemPath = item.path;
        const itemPathWithMain = itemPath.startsWith('/') ? `/main${itemPath}` : `/main/${itemPath}`;

        if (itemPath === currentPath || itemPathWithMain === currentPath) {
          setSelectedItem(item.title);
        }

        if (item.children) {
          item.children.forEach(child => {
            const childPath = child.path;
            const childPathWithMain = childPath.startsWith('/') ? `/main${childPath}` : `/main/${childPath}`;

            if (childPath === currentPath || childPathWithMain === currentPath) {
              setSelectedItem(child.title);
              setOpenKeys(prev => ({ ...prev, [item.title]: true }));
            }
          });
          checkPath(item.children);
        }
      });
    };

    checkPath(menuItems);
  }, [location.pathname, menuItems]);

  const toggleMenu = (key: string) => {
    setOpenKeys(prev => {
      const newState = { ...prev };
      if (!newState[key]) {
        Object.keys(newState).forEach(k => {
          newState[k] = k === key;
        });
      } else {
        newState[key] = !newState[key];
      }
      return newState;
    });
  };

  const handleMenuClick = (item: MenuItem) => {
    setSelectedItem(item.title);
  };

  const renderSectionItem = (title: string) => {
    return (
      <li className="nav-small-cap" key={`section-${title}`}>
        <FA6.FaEllipsis className="nav-small-cap-icon fs-4" />
        <span className="hide-menu">{title.toLowerCase()}</span>
      </li>
    );
  };

  const renderMenuItems = (items: MenuItem[]) => {
    const sections: string[] = [];
    const sectionItems: Record<string, MenuItem[]> = {};
    items.forEach(item => {
      const { section } = getPageSectionInfo(item.key);

      if (!sections.includes(section)) {
        sections.push(section);
        sectionItems[section] = [];
      }

      sectionItems[section].push(item);
    });

    return sections.flatMap(section => {
      const sectionElements = [renderSectionItem(section)];

      sectionItems[section].forEach(item => {
        const hasChildren = item.children && item.children.length > 0;
        const isOpen = openKeys[item.title];
        const isActive = selectedItem === item.title;
        const isParentOfActive = item.children?.some(child => selectedItem === child.title);

        sectionElements.push(
          <li className={`sidebar-item ${isParentOfActive ? 'selected' : ''} ${isActive ? 'active' : ''}`} key={`${item.key}-${item.title}`}>
            {hasChildren || item.isGroup ? (
              <>
                <a
                  className={`sidebar-link ${isParentOfActive || isActive ? 'active' : ''} has-arrow waves-effect waves-dark`}
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleMenu(item.title);
                  }}
                  aria-expanded={isOpen}
                >
                  <span className="d-flex">
                    {item.icon}
                  </span>
                  <span className="hide-menu">{item.title}</span>
                </a>
                <ul
                  aria-expanded={isOpen}
                  className={classNames("collapse first-level", {
                    'in': isOpen && layoutSettings.layout !== 'horizontal'
                  })}
                >
                  {item.children!.map((child) => (
                    <li className="sidebar-item" key={`${child.key}-${child.title}`}>
                      <Link
                        className={`sidebar-link sublink ${selectedItem === child.title ? 'active' : ''}`}
                        to={ensurePrefixedPath(child.path) || '#'}
                        onClick={() => handleMenuClick(child)}
                      >
                        <div className="round-16 d-flex align-items-center justify-content-center">
                          {child.icon}
                        </div>
                        <span className="hide-menu">{child.title}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </>
            ) : (
              <Link
                className={`sidebar-link waves-effect waves-dark ${isActive ? 'active' : ''}`}
                to={ensurePrefixedPath(item.path) || '#'}
                onClick={() => handleMenuClick(item)}
              >
                <span className="d-flex">
                  {item.icon}
                </span>
                <span className="hide-menu">{item.title}</span>
              </Link>
            )}
          </li>
        );
      });

      return sectionElements;
    });
  };

  return (
    <nav className="sidebar-nav">
      <ul id="sidebarnav">
        {renderMenuItems(menuItems)}
      </ul>
    </nav>
  );
};

export default Menu;
